from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .config import settings
from .constants import (
    API_DESCRIPTION,
    API_PREFIX,
    API_TITLE,
    API_VERSION,
    CORS_ALLOW_CREDENTIALS,
    CORS_ALLOW_HEADERS,
    CORS_ALLOW_METHODS,
    HTTP_STATUS_INTERNAL_SERVER_ERROR,
)
from .logging_config import get_logger
from .routers import files, multipart

logger = get_logger(__name__)


app = FastAPI(title=API_TITLE, description=API_DESCRIPTION, version=API_VERSION)


app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=CORS_ALLOW_CREDENTIALS,
    allow_methods=CORS_ALLOW_METHODS,
    allow_headers=CORS_ALLOW_HEADERS,
)


app.include_router(files.router, prefix=API_PREFIX)
app.include_router(multipart.router, prefix=f"{API_PREFIX}/files")


@app.get("/")
async def root():
    return {"message": API_TITLE, "version": API_VERSION, "docs": "/docs"}


@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "biormika-api"}


@app.exception_handler(Exception)
async def global_exception_handler(_, exc):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=HTTP_STATUS_INTERNAL_SERVER_ERROR,
        content={
            "error": "internal_server_error",
            "message": "An unexpected error occurred",
            "detail": str(exc) if settings.aws_region == "us-east-1" else None,
        },
    )
