# AWS Configuration
AWS_PROFILE = "biormika"
AWS_REGION = "us-east-1"

# File Configuration
MAX_FILE_SIZE_MB = 1024
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
ALLOWED_EXTENSIONS = (".edf", ".EDF")
ALLOWED_FILE_EXTENSIONS = [".edf", ".EDF"]
EDF_EXTENSION = ".edf"
EDF_FILES_PREFIX = "edf-files"

# S3 Configuration
S3_PREFIX = "edf-files/"
DEFAULT_EXPIRY_SECONDS = 3600
PRESIGNED_URL_EXPIRY_SECONDS = 3600
CONTENT_TYPE_OCTET_STREAM = "application/octet-stream"
METADATA_ORIGINAL_FILENAME = "original-filename"
S3_CLIENT_METHOD_PUT = "put_object"
S3_CLIENT_METHOD_GET = "get_object"
S3_CLIENT_METHOD_UPLOAD_PART = "upload_part"
S3_LIST_OBJECTS_METHOD = "list_objects_v2"
S3_ERROR_NOT_FOUND = "404"
S3_CORS_MAX_AGE_SECONDS = 3600
S3_MULTIPART_UPLOAD_CLEANUP_DAYS = 1
S3_CORS_EXPOSED_HEADERS = [
    "ETag",
    "x-amz-server-side-encryption",
    "x-amz-request-id",
    "x-amz-id-2",
]

# API Configuration
API_PREFIX = "/api/v1"
API_TITLE = "Biormika EDF File Upload API"
API_DESCRIPTION = "API for uploading and managing EDF files for HFO analysis"
API_VERSION = "1.0.0"

# Network Configuration
VITE_DEV_PORT = 5173
REACT_ALT_PORT = 3000
FASTAPI_PORT = 8000

# CORS Configuration
DEFAULT_ALLOWED_ORIGINS = [
    f"http://localhost:{VITE_DEV_PORT}",
    f"http://localhost:{REACT_ALT_PORT}",
    f"http://localhost:{FASTAPI_PORT}",
]
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = ["*"]
CORS_ALLOW_HEADERS = ["*"]

# Logging Configuration
LOGGING_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOGGING_LEVEL = "INFO"

# HTTP Status Codes
HTTP_STATUS_OK = 200
HTTP_STATUS_BAD_REQUEST = 400
HTTP_STATUS_NOT_FOUND = 404
HTTP_STATUS_INTERNAL_SERVER_ERROR = 500

# Lambda Configuration
LAMBDA_RESPONSE_HEADERS = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
}

