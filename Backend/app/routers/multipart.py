"""
Multipart upload operations router
"""

from fastapi import APIRouter

from ..config import settings
from ..models import (
    ErrorResponse,
    MultipartAbortRequest,
    MultipartBatchPartUrlsRequest,
    MultipartBatchPartUrlsResponse,
    MultipartCompleteRequest,
    MultipartInitiateRequest,
    MultipartInitiateResponse,
    MultipartPartUrlRequest,
    MultipartPartUrlResponse,
)
from ..services.s3_service import s3_service
from ..utils.error_handlers import handle_s3_error
from ..utils.file_utils import generate_file_key

router = APIRouter(prefix="/multipart", tags=["multipart"])


# Multipart upload initiation
@router.post(
    "/initiate",
    response_model=MultipartInitiateResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid file type or size"},
        500: {"model": ErrorResponse, "description": "Server error"},
    },
)
async def initiate_multipart_upload(request: MultipartInitiateRequest):
    """Initiate multipart upload process"""
    try:
        key = generate_file_key(request.filename)
        upload_id = s3_service.initiate_multipart_upload(key, request.filename)
        return MultipartInitiateResponse(
            upload_id=upload_id,
            key=key,
            expires_in=settings.presigned_url_expiry_seconds,
        )
    except Exception as e:
        handle_s3_error("initiate multipart upload", e)


# Part URL generation
@router.post(
    "/part-url",
    response_model=MultipartPartUrlResponse,
    responses={500: {"model": ErrorResponse, "description": "Server error"}},
)
async def get_multipart_part_url(request: MultipartPartUrlRequest):
    """Generate presigned URL for single part"""
    try:
        url = s3_service.generate_part_url(
            request.key, request.upload_id, request.part_number
        )
        return MultipartPartUrlResponse(
            url=url, expires_in=settings.presigned_url_expiry_seconds
        )
    except Exception as e:
        handle_s3_error("generate multipart part url", e)


@router.post(
    "/part-urls",
    response_model=MultipartBatchPartUrlsResponse,
    responses={500: {"model": ErrorResponse, "description": "Server error"}},
)
async def get_multipart_part_urls(request: MultipartBatchPartUrlsRequest):
    """Generate presigned URLs for multiple parts"""
    try:
        parts = [
            {
                "part_number": n,
                "url": s3_service.generate_part_url(request.key, request.upload_id, n),
            }
            for n in sorted(request.part_numbers)
        ]
        return MultipartBatchPartUrlsResponse(
            parts=parts,
            expires_in=settings.presigned_url_expiry_seconds,
        )
    except Exception as e:
        handle_s3_error("generate multipart batch part urls", e)


# Multipart upload completion
@router.post(
    "/complete",
    responses={500: {"model": ErrorResponse, "description": "Server error"}},
)
async def complete_multipart_upload(request: MultipartCompleteRequest):
    """Complete multipart upload process"""
    try:
        parts = sorted(
            [{"ETag": p.ETag, "PartNumber": p.PartNumber} for p in request.parts],
            key=lambda x: x["PartNumber"],
        )
        s3_service.complete_multipart_upload(request.key, request.upload_id, parts)
        return {"success": True, "key": request.key}
    except Exception as e:
        handle_s3_error("complete multipart upload", e)


@router.post(
    "/abort",
    responses={500: {"model": ErrorResponse, "description": "Server error"}},
)
async def abort_multipart_upload(request: MultipartAbortRequest):
    """Abort multipart upload process"""
    try:
        s3_service.abort_multipart_upload(request.key, request.upload_id)
        return {"success": True}
    except Exception as e:
        handle_s3_error("abort multipart upload", e)