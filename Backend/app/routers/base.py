"""
Base router utilities for common patterns
"""

from typing import Callable, Dict, Any
from functools import wraps

from fastapi import APIRouter, HTTPException

from ..services.s3_service import s3_service
from ..utils.error_handlers import handle_s3_error, handle_not_found
from ..logging_config import get_logger

logger = get_logger(__name__)


class BaseRouter:
    """Base class for routers with common S3 operations"""

    def __init__(self, prefix: str, tags: list[str]):
        self.router = APIRouter(prefix=prefix, tags=tags)
        self.s3_service = s3_service

    def with_s3_error_handling(self, operation_name: str):
        """Decorator to handle S3 errors consistently"""
        def decorator(func: Callable):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except HTTPException:
                    raise
                except Exception as e:
                    handle_s3_error(operation_name, e, kwargs.get("key"))
            return wrapper
        return decorator

    def validate_file_exists(self, key: str, resource_name: str = "File"):
        """Check if file exists in S3"""
        metadata = self.s3_service.get_file_metadata(key)
        if not metadata:
            handle_not_found(resource_name, key)
        return metadata

    def create_response(self, success: bool, message: str, **kwargs) -> Dict[str, Any]:
        """Create standardized response"""
        response = {"success": success, "message": message}
        response.update(kwargs)
        return response