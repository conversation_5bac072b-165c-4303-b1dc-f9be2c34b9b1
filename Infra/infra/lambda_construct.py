from aws_cdk import Duration
from aws_cdk import aws_apigateway as apigw
from aws_cdk import aws_iam as iam
from aws_cdk import aws_lambda as _lambda
from aws_cdk import aws_logs as logs
from constructs import Construct

from .config import (
    API_STAGE_NAME,
    API_THROTTLE_BURST_LIMIT,
    API_THROTTLE_RATE_LIMIT,
    LAMBDA_ARCHITECTURE,
    LAMBDA_HANDLER,
    LAMBDA_LOG_GROUP_NAME,
    LAMBDA_MEMORY_SIZE_MB,
    LAMBDA_RUNTIME_PYTHON,
    LAMBDA_TIMEOUT_SECONDS,
)
from .helpers import (
    create_api_cors_options,
    create_cfn_output,
    create_s3_read_write_policy,
)


class LambdaApiConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        bucket_name: str,
        backend_role: iam.Role,
        allowed_origins: list[str],
    ) -> None:
        super().__init__(scope, construct_id)

        self.bucket_name = bucket_name
        self.backend_role = backend_role
        self.allowed_origins = allowed_origins

        # Create Lambda components
        self._create_lambda_execution_role()
        self._create_log_group()
        self._create_lambda_function()

        # Create API Gateway
        self._create_api_gateway()

        # Create outputs
        self._create_outputs()

    def _create_lambda_execution_role(self) -> None:
        """Create IAM role for Lambda execution."""
        self.lambda_execution_role = iam.Role(
            self,
            "LambdaExecutionRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            description="Execution role for Biormika Lambda API",
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaBasicExecutionRole"
                )
            ],
        )

        # Add S3 permissions
        self.lambda_execution_role.add_to_policy(
            create_s3_read_write_policy(self.bucket_name)
        )

    def _create_log_group(self) -> None:
        """Create CloudWatch log group for Lambda function."""
        self.log_group = logs.LogGroup(
            self,
            "ApiLogGroup",
            log_group_name=LAMBDA_LOG_GROUP_NAME,
            retention=logs.RetentionDays.ONE_WEEK,
        )

    def _create_lambda_function(self) -> None:
        """Create the Lambda function."""
        self.api_function = _lambda.Function(
            self,
            "ApiFunction",
            runtime=getattr(_lambda.Runtime, LAMBDA_RUNTIME_PYTHON),
            architecture=getattr(_lambda.Architecture, LAMBDA_ARCHITECTURE),
            handler=LAMBDA_HANDLER,
            code=_lambda.Code.from_asset("../Backend"),
            timeout=Duration.seconds(LAMBDA_TIMEOUT_SECONDS),
            memory_size=LAMBDA_MEMORY_SIZE_MB,
            role=self.lambda_execution_role,
            environment=self._get_lambda_environment_variables(),
            log_group=self.log_group,
            description="Biormika EDF File Management API",
        )

    def _get_lambda_environment_variables(self) -> dict[str, str]:
        """Get environment variables for Lambda function."""
        return {
            "S3_BUCKET_NAME": self.bucket_name,
            "ALLOWED_ORIGINS": ",".join(self.allowed_origins),
            "LAMBDA_ENVIRONMENT": "true",
            "S3_TRANSFER_ACCELERATION": "true",
        }

    def _create_api_gateway(self) -> None:
        """Create API Gateway for Lambda function."""
        cors_options = create_api_cors_options(self.allowed_origins)

        self.api = apigw.LambdaRestApi(
            self,
            "ApiGateway",
            handler=self.api_function,
            proxy=True,
            description="Biormika EDF File Management API Gateway",
            deploy_options=self._get_api_deploy_options(),
            default_cors_preflight_options=apigw.CorsOptions(**cors_options),
            endpoint_configuration=apigw.EndpointConfiguration(
                types=[apigw.EndpointType.REGIONAL]
            ),
        )

    def _get_api_deploy_options(self) -> apigw.StageOptions:
        """Get deployment options for API Gateway stage."""
        return apigw.StageOptions(
            stage_name=API_STAGE_NAME,
            throttling_rate_limit=API_THROTTLE_RATE_LIMIT,
            throttling_burst_limit=API_THROTTLE_BURST_LIMIT,
            metrics_enabled=True,
        )

    def _create_outputs(self) -> None:
        """Create CloudFormation outputs."""
        create_cfn_output(
            self,
            "ApiGatewayUrl",
            value=self.api.url,
            description="API Gateway URL for the Biormika API",
            export_name="BiormikApiGatewayUrl",
        )

        create_cfn_output(
            self,
            "ApiGatewayId",
            value=self.api.rest_api_id,
            description="API Gateway ID",
            export_name="BiormikaApiGatewayId",
        )

        create_cfn_output(
            self,
            "LambdaFunctionArn",
            value=self.api_function.function_arn,
            description="Lambda function ARN",
            export_name="BiormikaLambdaFunctionArn",
        )
