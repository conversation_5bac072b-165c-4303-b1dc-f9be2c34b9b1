from aws_cdk import Stack, Tags
from constructs import Construct

from .config import DEFAULT_ALLOWED_ORIGINS, ENVIRONMENT, PROJECT_NAME, PURPOSE
from .lambda_construct import LambdaApiConstruct
from .s3_construct import S3StorageConstruct


class InfraStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        # Create S3 storage infrastructure
        s3_storage = S3StorageConstruct(
            self, "S3Storage", bucket_name=None, allowed_origins=None
        )

        # Create Lambda API infrastructure
        LambdaApiConstruct(
            self,
            "LambdaApi",
            bucket_name=s3_storage.edf_bucket.bucket_name,
            backend_role=s3_storage.backend_role,
            allowed_origins=DEFAULT_ALLOWED_ORIGINS,
        )

        # Add stack tags
        Tags.of(self).add("Project", PROJECT_NAME)
        Tags.of(self).add("Environment", ENVIRONMENT)
        Tags.of(self).add("Purpose", PURPOSE)
