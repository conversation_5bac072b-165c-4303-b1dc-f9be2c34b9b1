/**
 * Multipart upload service for handling all file uploads
 * Optimized for files from 1KB to 1.3GB with dynamic chunking and concurrency
 */

import { fileService } from "./api";
import { UPLOAD_CONFIG } from "@/constants";
import type { CompletedPart } from "@/types/api";

export class MultipartUploadService {
  /**
   * Upload a file using multipart upload with dynamic optimization
   * Automatically adjusts chunk size and concurrency based on file size
   */
  static async uploadFile(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<{ key: string; upload_id: string }> {
    let uploadId: string | undefined;
    let key: string | undefined;

    try {
      // Initiate multipart upload
      const initResponse = await fileService.initiateMultipart(file.name, file.size);
      uploadId = initResponse.upload_id;
      key = initResponse.key;

      // Calculate optimal settings based on file size
      const { chunkSize, totalParts, concurrency } = this.calculateOptimalSettings(file.size);

      // Upload parts
      const parts = await this.uploadParts(
        file,
        key,
        uploadId,
        chunkSize,
        totalParts,
        concurrency,
        onProgress
      );

      // Complete multipart upload
      await fileService.completeMultipart(key, uploadId, parts);
      onProgress?.(100);

      return { key, upload_id: uploadId };
    } catch (error) {
      // Automatically abort upload on failure
      if (key && uploadId) {
        await this.abortUpload(key, uploadId);
      }
      throw error;
    }
  }

  /**
   * Calculate optimal settings based on file size
   * Uses dynamic sizing for better performance with different file sizes
   */
  private static calculateOptimalSettings(fileSize: number): {
    chunkSize: number;
    totalParts: number;
    concurrency: number;
  } {
    const { DYNAMIC_SIZING, MIN_PART_SIZE, MAX_PART_SIZE, TARGET_PARTS } = UPLOAD_CONFIG.MULTIPART;

    let chunkSize: number;
    let concurrency: number;

    // Determine optimal chunk size and concurrency based on file size
    if (fileSize <= DYNAMIC_SIZING.SMALL.maxSize) {
      // Small files (< 100MB)
      chunkSize = DYNAMIC_SIZING.SMALL.chunkSize;
      concurrency = DYNAMIC_SIZING.SMALL.concurrency;
    } else if (fileSize <= DYNAMIC_SIZING.MEDIUM.maxSize) {
      // Medium files (100MB - 500MB)
      chunkSize = DYNAMIC_SIZING.MEDIUM.chunkSize;
      concurrency = DYNAMIC_SIZING.MEDIUM.concurrency;
    } else {
      // Large files (> 500MB, up to 1.3GB)
      chunkSize = DYNAMIC_SIZING.LARGE.chunkSize;
      concurrency = DYNAMIC_SIZING.LARGE.concurrency;
    }

    // For very small files, adjust chunk size to avoid too many tiny parts
    if (fileSize < MIN_PART_SIZE * 2) {
      // File is very small, use single part (but still use multipart API)
      chunkSize = fileSize;
    } else {
      // Calculate based on target parts, but respect min/max bounds
      const targetChunkSize = Math.ceil(fileSize / TARGET_PARTS);
      chunkSize = Math.min(Math.max(chunkSize, MIN_PART_SIZE), MAX_PART_SIZE);

      // Adjust if calculated size is better than default
      if (targetChunkSize > MIN_PART_SIZE && targetChunkSize < MAX_PART_SIZE) {
        chunkSize = targetChunkSize;
      }
    }

    const totalParts = Math.ceil(fileSize / chunkSize);

    // Adjust concurrency based on actual part count
    concurrency = Math.min(concurrency, totalParts);

    return { chunkSize, totalParts, concurrency };
  }

  /**
   * Get optimal concurrency based on hardware and file characteristics
   */
  private static getOptimalConcurrency(fileSize: number, totalParts: number): number {
    const { MAX_CONCURRENCY, DEFAULT_CONCURRENCY } = UPLOAD_CONFIG.MULTIPART;

    // Use hardware concurrency as a baseline
    const hardwareConcurrency =
      typeof navigator !== "undefined" && typeof navigator.hardwareConcurrency === "number"
        ? navigator.hardwareConcurrency
        : DEFAULT_CONCURRENCY;

    // Use half of hardware threads for upload to avoid overwhelming the system
    const baselineConcurrency = Math.floor(hardwareConcurrency / 2);

    // Apply dynamic sizing rules
    const { DYNAMIC_SIZING } = UPLOAD_CONFIG.MULTIPART;
    let targetConcurrency: number;

    if (fileSize <= DYNAMIC_SIZING.SMALL.maxSize) {
      targetConcurrency = DYNAMIC_SIZING.SMALL.concurrency;
    } else if (fileSize <= DYNAMIC_SIZING.MEDIUM.maxSize) {
      targetConcurrency = DYNAMIC_SIZING.MEDIUM.concurrency;
    } else {
      targetConcurrency = DYNAMIC_SIZING.LARGE.concurrency;
    }

    // Take the minimum of various limits
    return Math.min(
      targetConcurrency,
      baselineConcurrency,
      MAX_CONCURRENCY,
      totalParts
    );
  }

  /**
   * Upload all parts with concurrency control and progress tracking
   */
  private static async uploadParts(
    file: File,
    key: string,
    uploadId: string,
    chunkSize: number,
    totalParts: number,
    concurrency: number,
    onProgress?: (percent: number) => void
  ): Promise<CompletedPart[]> {
    const parts: CompletedPart[] = [];
    const partProgress = new Map<number, number>();

    // Progress tracking with per-part granularity
    const updateProgress = () => {
      if (!onProgress) return;
      const totalProgress = Array.from(partProgress.values()).reduce((sum, val) => sum + val, 0);
      const percent = Math.min(99, Math.round((totalProgress / file.size) * 100));
      onProgress(percent);
    };

    // Prefetch all part URLs for better performance
    const partUrls = await this.prefetchPartUrls(key, uploadId, totalParts);

    // Upload part function with progress tracking
    const uploadPart = async (partNumber: number) => {
      const start = (partNumber - 1) * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const blob = file.slice(start, end);
      const url = partUrls.get(partNumber)!;

      const eTag = await this.uploadPartWithRetry(
        blob,
        url,
        partNumber,
        (loaded) => {
          partProgress.set(partNumber, loaded);
          updateProgress();
        }
      );

      parts.push({ ETag: eTag, PartNumber: partNumber });
    };

    // Execute uploads with worker pool pattern
    await this.executeWithWorkerPool(totalParts, concurrency, uploadPart);

    // Sort parts by part number (required by S3)
    return parts.sort((a, b) => a.PartNumber - b.PartNumber);
  }

  /**
   * Prefetch all part URLs in a single batch for better performance
   */
  private static async prefetchPartUrls(
    key: string,
    uploadId: string,
    totalParts: number
  ): Promise<Map<number, string>> {
    const allPartNumbers = Array.from({ length: totalParts }, (_, i) => i + 1);

    // For large number of parts, batch the requests
    const maxBatchSize = 100;
    const urlMap = new Map<number, string>();

    if (totalParts <= maxBatchSize) {
      // Single batch for small uploads
      const batch = await fileService.getBatchPartUploadUrls(key, uploadId, allPartNumbers);
      batch.parts.forEach(p => urlMap.set(p.part_number, p.url));
    } else {
      // Multiple batches for large uploads
      const batches = [];
      for (let i = 0; i < totalParts; i += maxBatchSize) {
        const batchPartNumbers = allPartNumbers.slice(i, i + maxBatchSize);
        batches.push(fileService.getBatchPartUploadUrls(key, uploadId, batchPartNumbers));
      }

      const results = await Promise.all(batches);
      results.forEach(batch => {
        batch.parts.forEach(p => urlMap.set(p.part_number, p.url));
      });
    }

    return urlMap;
  }

  /**
   * Upload a single part with exponential backoff retry logic
   */
  private static async uploadPartWithRetry(
    blob: Blob,
    url: string,
    partNumber: number,
    onProgress: (loaded: number) => void
  ): Promise<string> {
    const { RETRY, TIMEOUTS } = UPLOAD_CONFIG.MULTIPART;

    return new Promise((resolve, reject) => {
      const attempt = (retries: number, delayMs: number) => {
        const xhr = new XMLHttpRequest();

        xhr.open("PUT", url);
        xhr.timeout = TIMEOUTS.PART_UPLOAD;

        xhr.upload.onprogress = (ev: ProgressEvent) => {
          if (ev.lengthComputable) {
            onProgress(ev.loaded);
          }
        };

        const handleError = (reason: string) => {
          if (retries > 0) {
            // Reset progress for this part on retry
            onProgress(0);

            // Exponential backoff with jitter
            const jitter = Math.random() * 0.3 + 0.85; // 0.85 to 1.15
            const nextDelay = Math.min(
              delayMs * RETRY.BACKOFF_MULTIPLIER * jitter,
              RETRY.MAX_DELAY
            );

            setTimeout(() => attempt(retries - 1, nextDelay), delayMs);
          } else {
            reject(new Error(reason));
          }
        };

        xhr.onerror = () => handleError(`Network error uploading part ${partNumber}`);
        xhr.ontimeout = () => handleError(`Timeout uploading part ${partNumber}`);

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            const eTag = xhr.getResponseHeader("ETag") || "";
            resolve(eTag.replaceAll('"', ""));
          } else {
            handleError(`Part ${partNumber} upload failed (HTTP ${xhr.status})`);
          }
        };

        xhr.send(blob);
      };

      attempt(RETRY.MAX_ATTEMPTS - 1, RETRY.INITIAL_DELAY);
    });
  }

  /**
   * Execute tasks with worker pool pattern for controlled concurrency
   */
  private static async executeWithWorkerPool(
    totalTasks: number,
    workerCount: number,
    taskFn: (taskNumber: number) => Promise<void>
  ): Promise<void> {
    const queue = Array.from({ length: totalTasks }, (_, i) => i + 1);
    const errors: Error[] = [];

    const workers = Array.from({ length: Math.min(workerCount, totalTasks) }, async () => {
      while (queue.length > 0) {
        const taskNumber = queue.shift();
        if (!taskNumber) break;

        try {
          await taskFn(taskNumber);
        } catch (error) {
          // Collect errors but continue with other parts
          errors.push(error as Error);

          // If too many errors, stop processing
          if (errors.length >= Math.ceil(totalTasks * 0.3)) {
            queue.length = 0; // Clear queue to stop other workers
          }
        }
      }
    });

    await Promise.all(workers);

    // If there were errors, throw the first one
    if (errors.length > 0) {
      throw new Error(`Upload failed: ${errors[0].message} (and ${errors.length - 1} more errors)`);
    }
  }

  /**
   * Abort multipart upload - best effort, doesn't throw
   */
  static async abortUpload(key: string, uploadId: string): Promise<void> {
    try {
      await Promise.race([
        fileService.abortMultipart(key, uploadId),
        new Promise(resolve =>
          setTimeout(resolve, UPLOAD_CONFIG.MULTIPART.TIMEOUTS.ABORT)
        )
      ]);
    } catch (error) {
      // Log but don't throw - abort is best effort
      console.error("Failed to abort multipart upload:", error);
    }
  }
}