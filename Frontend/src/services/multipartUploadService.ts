/**
 * Multipart upload service for handling large file uploads
 */

import { fileService } from "./api";
import type { CompletedPart } from "@/types/api";

// Upload configuration constants
const MB = 1024 * 1024;
const MIN_PART_SIZE = 8 * MB;
const MAX_PART_SIZE = 64 * MB;
const TARGET_PARTS = 24;
const MAX_CONCURRENCY = 8;
const PART_UPLOAD_TIMEOUT = 5 * 60 * 1000; // 5 minutes
const RETRY_COUNT = 3;
const INITIAL_RETRY_DELAY = 500;
const MAX_RETRY_DELAY = 8000;

export class MultipartUploadService {
  /**
   * Upload a large file using multipart upload
   */
  static async uploadLargeFile(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<{ key: string; upload_id: string }> {
    // Initiate multipart upload
    const { upload_id, key } = await fileService.initiateMultipart(file.name, file.size);

    // Calculate chunk size and part count
    const { chunkSize, totalParts } = this.calculateChunkSize(file.size);

    // Upload parts
    const parts = await this.uploadParts(file, key, upload_id, chunkSize, totalParts, onProgress);

    // Complete multipart upload
    await fileService.completeMultipart(key, upload_id, parts);
    onProgress?.(100);

    return { key, upload_id };
  }

  /**
   * Calculate optimal chunk size for multipart upload
   */
  private static calculateChunkSize(fileSize: number): { chunkSize: number; totalParts: number } {
    let chunkSize = Math.ceil(fileSize / TARGET_PARTS);
    chunkSize = Math.min(Math.max(chunkSize, MIN_PART_SIZE), MAX_PART_SIZE);
    const totalParts = Math.ceil(fileSize / chunkSize);
    return { chunkSize, totalParts };
  }

  /**
   * Get optimal concurrency based on hardware
   */
  private static getOptimalConcurrency(totalParts: number): number {
    const hardwareConcurrency = typeof navigator !== "undefined" && typeof navigator.hardwareConcurrency === "number"
      ? navigator.hardwareConcurrency
      : 8;
    return Math.min(
      Math.max(6, Math.floor(hardwareConcurrency / 2)),
      MAX_CONCURRENCY,
      totalParts
    );
  }

  /**
   * Upload all parts with concurrency control
   */
  private static async uploadParts(
    file: File,
    key: string,
    uploadId: string,
    chunkSize: number,
    totalParts: number,
    onProgress?: (percent: number) => void
  ): Promise<CompletedPart[]> {
    const parts: CompletedPart[] = [];
    let uploadedBytes = 0;
    const concurrency = this.getOptimalConcurrency(totalParts);

    // Progress tracking
    const updateProgress = () => {
      if (!onProgress) return;
      const percent = Math.min(100, Math.round((uploadedBytes / file.size) * 100));
      onProgress(percent);
    };

    // Prefetch all part URLs
    const partUrls = await this.prefetchPartUrls(key, uploadId, totalParts);

    // Upload part function
    const uploadPart = async (partNumber: number) => {
      const start = (partNumber - 1) * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const blob = file.slice(start, end);
      const url = partUrls.get(partNumber)!;

      const eTag = await this.uploadPartWithRetry(
        blob,
        url,
        partNumber,
        (delta) => {
          uploadedBytes += delta;
          updateProgress();
        }
      );

      parts.push({ ETag: eTag, PartNumber: partNumber });
    };

    // Execute uploads with worker pool
    await this.executeWithWorkerPool(totalParts, concurrency, uploadPart);

    // Sort parts by part number
    return parts.sort((a, b) => a.PartNumber - b.PartNumber);
  }

  /**
   * Prefetch all part URLs for better performance
   */
  private static async prefetchPartUrls(
    key: string,
    uploadId: string,
    totalParts: number
  ): Promise<Map<number, string>> {
    const allPartNumbers = Array.from({ length: totalParts }, (_, i) => i + 1);
    const batch = await fileService.getBatchPartUploadUrls(key, uploadId, allPartNumbers);
    return new Map(batch.parts.map(p => [p.part_number, p.url]));
  }

  /**
   * Upload a single part with retry logic
   */
  private static async uploadPartWithRetry(
    blob: Blob,
    url: string,
    partNumber: number,
    onProgress: (delta: number) => void
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const attempt = (retries: number, delayMs: number) => {
        const xhr = new XMLHttpRequest();
        let lastLoaded = 0;

        xhr.open("PUT", url);
        xhr.timeout = PART_UPLOAD_TIMEOUT;

        xhr.upload.onprogress = (ev: ProgressEvent) => {
          if (ev.lengthComputable) {
            const delta = ev.loaded - lastLoaded;
            lastLoaded = ev.loaded;
            onProgress(delta);
          }
        };

        const handleError = (reason: string) => {
          if (retries > 0) {
            const nextDelay = Math.min(delayMs * 2, MAX_RETRY_DELAY);
            setTimeout(() => attempt(retries - 1, nextDelay), delayMs);
          } else {
            reject(new Error(reason));
          }
        };

        xhr.onerror = () => handleError(`Network error uploading part ${partNumber}`);
        xhr.ontimeout = () => handleError(`Timeout uploading part ${partNumber}`);

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            const eTag = xhr.getResponseHeader("ETag") || "";
            resolve(eTag.replaceAll('"', ""));
          } else {
            handleError(`Part ${partNumber} upload failed (${xhr.status})`);
          }
        };

        xhr.send(blob);
      };

      attempt(RETRY_COUNT, INITIAL_RETRY_DELAY);
    });
  }

  /**
   * Execute tasks with worker pool pattern
   */
  private static async executeWithWorkerPool(
    totalTasks: number,
    workerCount: number,
    taskFn: (taskNumber: number) => Promise<void>
  ): Promise<void> {
    const queue = Array.from({ length: totalTasks }, (_, i) => i + 1);

    const workers = Array.from({ length: Math.min(workerCount, totalTasks) }, async () => {
      while (queue.length > 0) {
        const taskNumber = queue.shift();
        if (!taskNumber) break;
        await taskFn(taskNumber);
      }
    });

    await Promise.all(workers);
  }

  /**
   * Abort multipart upload
   */
  static async abortUpload(key: string, uploadId: string): Promise<void> {
    try {
      await fileService.abortMultipart(key, uploadId);
    } catch (error) {
      // Log but don't throw - abort is best effort
      console.error("Failed to abort multipart upload:", error);
    }
  }
}