/**
 * File operations service for managing file uploads and downloads
 */

import { fileService } from "./api";
import { MultipartUploadService } from "./multipartUploadService";
import { ERROR_MESSAGES } from "@/constants";
import type { FileInfo } from "@/types/api";

// Upload configuration
const MB = 1024 * 1024;
const SINGLE_PUT_THRESHOLD = 64 * MB;

export type OperationResult<T = void> = {
  success: boolean;
  data?: T;
  error?: string;
};

export class FileOperationsService {
  /**
   * Upload a file (automatically chooses single or multipart upload)
   */
  static async uploadFile(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<OperationResult> {
    try {
      // Small file: use single PUT upload
      if (file.size <= SINGLE_PUT_THRESHOLD) {
        return await this.uploadSmallFile(file, onProgress);
      }

      // Large file: use multipart upload
      return await this.uploadLargeFile(file, onProgress);
    } catch (error) {
      return this.handleError(error, ERROR_MESSAGES.UPLOAD_FAILED);
    }
  }

  /**
   * Upload small file using single PUT
   */
  private static async uploadSmallFile(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<OperationResult> {
    const presigned = await fileService.getPresignedUrl({
      filename: file.name,
      filesize: file.size,
    });
    await fileService.uploadFile(file, presigned.url, onProgress);
    return { success: true };
  }

  /**
   * Upload large file using multipart upload
   */
  private static async uploadLargeFile(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<OperationResult> {
    let uploadState: { key?: string; upload_id?: string } = {};

    try {
      uploadState = await MultipartUploadService.uploadLargeFile(file, onProgress);
      return { success: true };
    } catch (error) {
      // Try to abort on failure
      if (uploadState.key && uploadState.upload_id) {
        await MultipartUploadService.abortUpload(uploadState.key, uploadState.upload_id);
      }
      throw error;
    }
  }

  /**
   * Delete a file from storage
   */
  static async deleteFile(key: string): Promise<OperationResult> {
    try {
      await fileService.deleteFile(key);
      return { success: true };
    } catch (error) {
      return this.handleError(error, ERROR_MESSAGES.DELETE_FAILED);
    }
  }

  /**
   * Fetch list of files from storage
   */
  static async fetchFiles(): Promise<OperationResult<FileInfo[]>> {
    try {
      const response = await fileService.listFiles();
      return { success: true, data: response.files };
    } catch (error) {
      return this.handleError(error, ERROR_MESSAGES.FETCH_FAILED);
    }
  }

  /**
   * Get download URL for a file
   */
  static async getDownloadUrl(key: string): Promise<OperationResult<string>> {
    try {
      const response = await fileService.getDownloadUrl(key);
      return { success: true, data: response.download_url };
    } catch (error) {
      return this.handleError(error, ERROR_MESSAGES.DOWNLOAD_FAILED);
    }
  }

  /**
   * Perform generic file action with error handling
   */
  static async performFileAction<T>(
    action: () => Promise<T>,
    errorMessage?: string
  ): Promise<OperationResult<T>> {
    try {
      const result = await action();
      return { success: true, data: result };
    } catch (error) {
      return this.handleError(error, errorMessage || "Operation failed");
    }
  }

  /**
   * Handle errors consistently
   */
  private static handleError<T = void>(error: unknown, defaultMessage: string): OperationResult<T> {
    return {
      success: false,
      error: error instanceof Error ? error.message : defaultMessage,
    };
  }
}

export const fileOperations = FileOperationsService;