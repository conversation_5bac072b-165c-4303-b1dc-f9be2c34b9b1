const FILE_MAX_SIZE_MB = 1328; // 1.3GB
const FILE_ALLOWED_EXTENSIONS = [".edf"];

export const API_CONFIG = {
  BASE_URL:
    import.meta.env.VITE_API_BASE_URL ||
    (import.meta.env.PROD ? "https://ck4n7i81o4.execute-api.us-east-1.amazonaws.com/prod/api/v1" : "http://localhost:8000/api/v1"),
  ENDPOINTS: {
    FILES: "/files",
  },
  TIMEOUT_MS: 30000,
};

export const FILE_CONFIG = {
  MAX_SIZE_BYTES: 1328 * 1024 * 1024, // 1.3GB
  MAX_SIZE_MB: FILE_MAX_SIZE_MB,
  ALLOWED_EXTENSIONS: FILE_ALLOWED_EXTENSIONS,
  UPLOAD_SUCCESS_DELAY_MS: 2000,
};

const KB = 1024;
const MB = 1024 * KB;

export const UPLOAD_CONFIG = {
  MULTIPART: {
    THRESHOLD: 5 * MB, // 5MB - Use multipart for all files larger than this
    MIN_PART_SIZE: 5 * MB, // 5MB minimum part size (AWS S3 requirement)
    MAX_PART_SIZE: 100 * MB, // 100MB maximum part size
    DEFAULT_PART_SIZE: 10 * MB, // 10MB default part size
    MAX_PARTS: 10000, // AWS S3 limit
    TARGET_PARTS: 24, // Target number of parts for optimal performance

    // Dynamic sizing based on file size for optimal performance
    DYNAMIC_SIZING: {
      SMALL: {
        maxSize: 100 * MB,
        chunkSize: 5 * MB,
        concurrency: 3
      },
      MEDIUM: {
        maxSize: 500 * MB,
        chunkSize: 10 * MB,
        concurrency: 5
      },
      LARGE: {
        chunkSize: 20 * MB,
        concurrency: 8
      }
    },

    // Timeout configuration
    TIMEOUTS: {
      PART_UPLOAD: 5 * 60 * 1000, // 5 minutes per part
      COMPLETE: 30 * 1000, // 30 seconds to complete
      ABORT: 10 * 1000, // 10 seconds to abort
    },

    // Retry configuration
    RETRY: {
      MAX_ATTEMPTS: 3,
      INITIAL_DELAY: 500, // 500ms
      MAX_DELAY: 8000, // 8 seconds
      BACKOFF_MULTIPLIER: 2,
    },

    // Concurrency limits
    MAX_CONCURRENCY: 10,
    DEFAULT_CONCURRENCY: 8,
  }
};

export const UI_CONFIG = {
  NOTIFICATION_AUTO_HIDE_MS: 3000,
  CONFIRM_DIALOG_ANIMATION_MS: 200,
  DEBOUNCE_DELAY_MS: 300,
  LOADING_DELAY_MS: 500,
};

export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: `File size exceeds ${FILE_MAX_SIZE_MB}MB limit`,
  INVALID_FILE_TYPE: `Invalid file type. Only ${FILE_ALLOWED_EXTENSIONS.join(", ")} files are allowed`,
  UPLOAD_FAILED: "Failed to upload file. Please try again.",
  DELETE_FAILED: "Failed to delete file. Please try again.",
  FETCH_FAILED: "Failed to fetch files. Please try again.",
  DOWNLOAD_FAILED: "Failed to generate download link. Please try again.",
  NETWORK_ERROR: "Network error. Please check your connection.",
  UNKNOWN_ERROR: "An unexpected error occurred. Please try again.",
  FILE_EMPTY: "File cannot be empty",
  TIMEOUT_ERROR: "Request timed out. Please try again.",
};

export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: "File uploaded successfully",
  FILE_DELETED: "File deleted successfully",
  FILES_FETCHED: "Files loaded successfully",
};

export const TABLE_CONFIG = {
  PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
};

export const UI_CONSTANTS = {
  DEFAULT_USER_DISPLAY: "******",
  DEFAULT_USER_CREDITS: 300,
  ACTION_BUTTON_CLASSES: "bg-blue-600 hover:bg-blue-700 text-white px-6 py-2",
} as const;
